# 金蝶云未核销统计油猴脚本

## 简介

这是一个专门用于金蝶云系统的油猴脚本，可以自动统计各销售区域的未核销应付单数量和金额。通过在金蝶云网页界面中添加一个统计按钮，用户可以一键获取未核销数据的详细统计信息。

## 功能特性

- 📊 一键统计各销售区域未核销应付单数量
- 💰 显示未核销金额和占比分析
- 📈 数据可视化展示
- 💾 支持导出统计结果为CSV文件
- 🔄 自动识别页面数据或通过API获取数据
- 🎨 美观的UI界面设计

## 安装说明

1. 安装Tampermonkey浏览器扩展：
   - [Chrome扩展商店](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
   - [Firefox附加组件](https://addons.mozilla.org/firefox/addon/tampermonkey/)

2. 下载并安装脚本：
   - 打开 `金蝶云未核销统计.user.js` 文件
   - 点击"安装"按钮
   - 确认安装

## 使用方法

1. 登录金蝶云系统
2. 进入应付单查询页面
3. 页面右上角会出现"📊 统计未核销"按钮
4. 点击按钮开始统计
5. 查看统计结果并可导出为CSV文件

## 技术说明

### 数据识别

脚本会自动识别页面中的以下字段：
- 销售区域：包含"区"、"组"、"部"等关键词的字段
- 核销状态：包含"未核销"、"部分核销"、"全部核销"等状态的字段
- 单据编号：以"AP-"开头的编号
- 金额：包含"￥"符号或纯数字的字段

### API集成

如果页面没有直接显示数据，脚本会尝试通过金蝶云的API接口获取数据：
- 自动获取CSRF Token和用户ID
- 构造正确的API请求
- 解析返回的JSON数据

## 支持的金蝶云域名

- `*.kdyun.com`
- `*.kdgalaxy.com`

## 常见问题

### 1. 脚本无法识别数据怎么办？
确保您在正确的应付单查询页面，并且页面已完全加载。

### 2. API请求失败怎么办？
检查网络连接，确认已正确登录金蝶云系统。

### 3. 统计结果不准确怎么办？
检查页面显示的数据是否完整，或尝试刷新页面后重新统计。

## 更新日志

### v1.2 (最新)
- 修复数据获取问题，提高数据识别准确率
- 改进API调用机制，支持多种端点尝试
- 增强错误处理和日志记录
- 优化数据解析逻辑，支持更多数据格式

### v1.1
- 优化数据识别算法
- 增加API数据获取功能
- 改进UI界面设计
- 添加导出功能

### v1.0
- 初始版本发布
- 基础统计功能

## 故障排除

### 数据获取问题
如果脚本无法获取数据，请尝试以下解决方案：

1. **检查页面加载状态**：确保页面完全加载后再点击统计按钮
2. **刷新页面**：尝试刷新页面后重新统计
3. **检查网络连接**：确保网络连接正常
4. **查看浏览器控制台**：按F12打开开发者工具，查看Console中的错误信息
5. **手动触发数据加载**：在金蝶云页面上先执行一次数据查询操作

### 常见错误及解决方案

- **"未找到数据"**：可能是页面数据格式与脚本预期不匹配，建议联系开发者提供页面截图
- **API请求失败**：检查是否正确登录系统，或尝试使用页面数据提取功能
- **统计结果为空**：检查是否有未核销状态的数据存在

## 技术支持

如遇到问题，请提供以下信息以便快速定位问题：
1. 金蝶云系统版本信息
2. 浏览器版本和类型
3. 页面截图（隐藏敏感信息）
4. 浏览器控制台错误日志
5. 问题重现步骤

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发者。