// ==UserScript==
// @name         金蝶云未核销统计
// @namespace    http://tampermonkey.net/
// @version      1.2
// @description  统计金蝶云系统中各销售区域的未核销应付单数量
// <AUTHOR>
// @match        https://*.kdyun.com/*
// @match        https://*.kdgalaxy.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 存储拦截到的xlsx数据
    let interceptedXlsxData = null;

    // 拦截下载请求
    const originalOpen = XMLHttpRequest.prototype.open;
    const originalSend = XMLHttpRequest.prototype.send;
    
    XMLHttpRequest.prototype.open = function(method, url) {
        this._url = url;
        this._method = method;
        return originalOpen.apply(this, arguments);
    };
    
    XMLHttpRequest.prototype.send = function() {
        // 检查是否为下载请求
        if (this._url && this._url.includes('attachment/download.do') &&
            (this._url.includes('.xlsx') || this._url.includes('.xls'))) {
            
            // 监听请求完成
            this.addEventListener('load', function() {
                if (this.status === 200 && this.response) {
                    // 拦截到xlsx文件
                    console.log('拦截到xlsx下载请求:', this._url);
                    // 尝试获取响应数据
                    try {
                        // 创建Blob对象
                        const blob = new Blob([this.response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                        // 自动解析并统计
                        parseAndAnalyzeBlob(blob);
                    } catch (error) {
                        console.error('拦截数据处理失败:', error);
                    }
                }
            });
        }
        return originalSend.apply(this, arguments);
    };
    // 简化的xlsx解析函数
    function parseXlsxData(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    // 使用SheetJS库解析xlsx文件
                    const data = new Uint8Array(e.target.result);
                    const workbook = window.XLSX.read(data, { type: 'array' });
                    
                    // 获取第一个工作表
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    
                    // 转换为JSON格式
                    const jsonData = window.XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    resolve(jsonData);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = function(error) {
                reject(error);
            };
            reader.readAsArrayBuffer(file);
        });
    }

    // 解析Blob并自动分析
    function parseAndAnalyzeBlob(blob) {
        if (!window.XLSX) {
            console.warn('SheetJS库未加载，无法解析Excel文件');
            return;
        }
        
        // 显示加载状态
        showLoading();
        
        // 解析Blob
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                // 使用SheetJS库解析xlsx文件
                const data = new Uint8Array(e.target.result);
                const workbook = window.XLSX.read(data, { type: 'array' });
                
                // 获取第一个工作表
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];
                
                // 转换为JSON格式
                const jsonData = window.XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                
                // 提取数据
                const extractedData = extractDataFromExcel(jsonData);
                
                if (extractedData.length === 0) {
                    hideLoading();
                    alert('未从Excel文件中找到有效数据');
                    return;
                }
                
                // 自动分析并显示结果，隐藏金额列
                analyzeAndDisplayResults(extractedData, true);
            } catch (error) {
                hideLoading();
                console.error('解析Excel文件失败:', error);
                alert('解析Excel文件失败: ' + error.message);
            }
        };
        reader.onerror = function(error) {
            hideLoading();
            console.error('读取Excel文件失败:', error);
            alert('读取Excel文件失败: ' + error.message);
        };
        reader.readAsArrayBuffer(blob);
    }

    // 从Excel数据中提取统计信息
    function extractDataFromExcel(excelData) {
        if (!excelData || excelData.length === 0) return [];
        
        const data = [];
        // 假设第一行是标题行
        const headers = excelData[0];
        
        // 查找关键列的索引
        let salesRegionIndex = -1;
        let settleStatusIndex = -1;
        let amountIndex = -1;
        let billNoIndex = -1;
        
        // 查找标题行中的关键字段 - 更全面的匹配
        for (let i = 0; i < headers.length; i++) {
            const header = headers[i].toString().toLowerCase();
            if (header.includes('销售区域') || header.includes('ghg_salesdept_name') ||
                header.includes('部门') || header.includes('区域')) {
                salesRegionIndex = i;
            } else if (header.includes('核销状态') || header.includes('settlestatus') ||
                       header.includes('状态') || header.includes('勾稽状态')) {
                settleStatusIndex = i;
            } else if (header.includes('含税价') || header.includes('价税合计') ||
                       header.includes('金额') || header.includes('总价') ||
                       header.includes('pricetaxtotal')) {
                amountIndex = i;
            } else if (header.includes('单据编号') || header.includes('billno') ||
                       header.includes('编号') || header.includes('单号')) {
                billNoIndex = i;
            }
        }
        
        // 解析数据行
        for (let i = 1; i < excelData.length; i++) {
            const row = excelData[i];
            if (!row || row.length === 0) continue;
            
            // 获取单元格值并处理空值
            const getCellValue = (index) => {
                if (index < 0 || index >= row.length) return '';
                const value = row[index];
                return value !== null && value !== undefined ? value.toString().trim() : '';
            };
            
            const rowData = {
                salesRegion: getCellValue(salesRegionIndex),
                settleStatus: getCellValue(settleStatusIndex),
                amount: amountIndex >= 0 ? parseFloat(getCellValue(amountIndex).replace(/[￥¥,，]/g, '')) || 0 : 0,
                billNo: getCellValue(billNoIndex)
            };
            
            // 只有当有销售区域或核销状态时才添加数据
            if (rowData.salesRegion || rowData.settleStatus) {
                data.push(rowData);
            }
        }
        
        return data;
    }

    // 添加SheetJS库
    function addSheetJS() {
        // 检查是否已经加载了SheetJS
        if (window.XLSX) return;
        
        // 动态加载SheetJS库
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js';
        script.onload = function() {
            console.log('SheetJS库加载成功');
        };
        script.onerror = function() {
            console.error('SheetJS库加载失败');
        };
        document.head.appendChild(script);
    }

    // 等待页面加载完成
    window.addEventListener('load', function() {
        // 创建统计按钮
        createStatButton();
        // 添加SheetJS库
        addSheetJS();
    });

    // 创建统计按钮
    function createStatButton() {
        // 查找指定位置添加按钮
        const targetDiv = document.getElementById('accountorgflex');
        
        if (targetDiv) {
            // 创建按钮容器
            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = `
                display: inline-block;
                margin-left: 10px;
                vertical-align: middle;
            `;
            
            // 创建统计按钮
            const statButton = document.createElement('button');
            statButton.id = 'stat-unverified-btn';
            statButton.textContent = '📊 统计未核销';
            statButton.style.cssText = `
                padding: 5px 10px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                font-weight: bold;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            `;
            
            statButton.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 3px 10px rgba(0,0,0,0.3)';
            });
            
            statButton.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
            });
            
            statButton.addEventListener('click', analyzeUnverifiedData);
            
            // 创建开始加载数据按钮
            const loadButton = document.createElement('button');
            loadButton.id = 'load-data-btn';
            loadButton.textContent = '🔄 开始加载数据';
            loadButton.style.cssText = `
                padding: 5px 10px;
                background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                font-weight: bold;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
                margin-left: 5px;
            `;
            
            loadButton.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 3px 10px rgba(0,0,0,0.3)';
            });
            
            loadButton.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
           });
            
            // 创建开始加载数据按钮
            const loadButton = document.createElement('button');
            loadButton.id = 'load-data-btn';
            loadButton.textContent = '🔄 开始加载数据';
            loadButton.style.cssText = `
                padding: 5px 10px;
                background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                font-weight: bold;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
                margin-left: 5px;
            `;
            
            loadButton.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 3px 10px rgba(0,0,0,0.3)';
            });
            
            loadButton.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
            });
            
            loadButton.addEventListener('click', function() {
                // 触发页面数据加载
                loadDataFromPage();
            });
            
            // 创建从Excel文件统计按钮
            const excelStatButton = document.createElement('button');
            excelStatButton.id = 'stat-unverified-excel-btn';
            excelStatButton.textContent = '📊 从Excel文件统计';
            excelStatButton.style.cssText = `
                padding: 5px 10px;
                background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                font-weight: bold;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
                margin-left: 5px;
            `;
            
            excelStatButton.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 3px 10px rgba(0,0,0,0.3)';
            });
            
            excelStatButton.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
            });
            
            excelStatButton.addEventListener('click', analyzeUnverifiedDataFromExcel);
            
            // 将按钮添加到容器
            buttonContainer.appendChild(statButton);
            buttonContainer.appendChild(loadButton);
            buttonContainer.appendChild(excelStatButton);
            
            // 创建自动拦截统计按钮
            const autoInterceptButton = document.createElement('button');
            autoInterceptButton.id = 'auto-intercept-stat-btn';
            autoInterceptButton.textContent = '📊 自动拦截统计';
            autoInterceptButton.style.cssText = `
                padding: 5px 10px;
                background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                font-weight: bold;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
                margin-left: 5px;
            `;
            
            autoInterceptButton.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 3px 10px rgba(0,0,0,0.3)';
            });
            
            autoInterceptButton.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
            });
            
            autoInterceptButton.addEventListener('click', function() {
                // 提示用户点击导出按钮
                alert('请在金蝶云系统中点击导出Excel按钮，系统将自动拦截并统计Excel数据。');
            });
            
            // 将按钮添加到容器
            buttonContainer.appendChild(statButton);
            buttonContainer.appendChild(loadButton);
            buttonContainer.appendChild(excelStatButton);
            buttonContainer.appendChild(autoInterceptButton);
            
            // 将容器插入到目标div后面
            targetDiv.parentNode.insertBefore(buttonContainer, targetDiv.nextSibling);
        } else {
            // 如果找不到指定位置，回退到原来的方式
            const toolbar = document.querySelector('[id*="toolbar"]') || document.querySelector('.toolbar') || document.body;
            
            const button = document.createElement('button');
            button.id = 'stat-unverified-btn';
            button.textContent = '📊 统计未核销';
            button.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 9999;
                padding: 10px 15px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            `;
            
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 6px 20px rgba(0,0,0,0.3)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
            });
            
            button.addEventListener('click', analyzeUnverifiedData);
            document.body.appendChild(button);
            
            // 添加从Excel文件统计按钮
            const excelButton = document.createElement('button');
            excelButton.id = 'stat-unverified-excel-btn';
            excelButton.textContent = '📊 从Excel文件统计';
            excelButton.style.cssText = `
                position: fixed;
                top: 60px;
                right: 10px;
                z-index: 9999;
                padding: 10px 15px;
                background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            `;
            
            excelButton.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 6px 20px rgba(0,0,0,0.3)';
            });
            
            excelButton.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
            });
            
            excelButton.addEventListener('click', analyzeUnverifiedDataFromExcel);
            document.body.appendChild(excelButton);
            
            // 添加自动拦截统计按钮
            const autoInterceptButton = document.createElement('button');
            autoInterceptButton.id = 'auto-intercept-stat-btn';
            autoInterceptButton.textContent = '📊 自动拦截统计';
            autoInterceptButton.style.cssText = `
                position: fixed;
                top: 110px;
                right: 10px;
                z-index: 9999;
                padding: 10px 15px;
                background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            `;
            
            autoInterceptButton.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 6px 20px rgba(0,0,0,0.3)';
            });
            
            autoInterceptButton.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
            });
            
            autoInterceptButton.addEventListener('click', function() {
                // 提示用户点击导出按钮
                alert('请在金蝶云系统中点击导出Excel按钮，系统将自动拦截并统计Excel数据。');
            });
            
            document.body.appendChild(autoInterceptButton);
        }
    }

    // 分析未核销数据
    async function analyzeUnverifiedData() {
        try {
            showLoading();
            
            // 获取页面中的数据
            const data = extractDataFromPage();
            
            if (data.length === 0) {
                // 如果页面没有数据，尝试发送请求获取数据
                const fetchedData = await fetchDataFromAPI();
                if (fetchedData.length > 0) {
                    analyzeAndDisplayResults(fetchedData);
                } else {
                    // 如果API也没有数据，提示用户点击"开始加载数据"按钮
                    hideLoading();
                    alert('未找到数据，请点击"开始加载数据"按钮加载数据后再试。');
                }
            } else {
                analyzeAndDisplayResults(data);
            }
        } catch (error) {
            hideLoading();
            console.error('统计失败:', error);
            alert('统计失败: ' + error.message);
        }
    }

    // 从Excel文件分析未核销数据
    async function analyzeUnverifiedDataFromExcel() {
        try {
            // 检查SheetJS库是否已加载
            if (!window.XLSX) {
                alert('Excel解析库未加载，请稍后重试');
                return;
            }
            
            // 创建文件选择输入框
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = '.xlsx,.xls';
            fileInput.style.display = 'none';
            
            // 添加到页面
            document.body.appendChild(fileInput);
            
            // 设置文件选择事件
            fileInput.addEventListener('change', async function(event) {
                const file = event.target.files[0];
                if (!file) return;
                
                try {
                    showLoading();
                    
                    // 解析Excel文件
                    const excelData = await parseXlsxData(file);
                    
                    // 提取数据
                    const data = extractDataFromExcel(excelData);
                    
                    if (data.length === 0) {
                        hideLoading();
                        alert('未从Excel文件中找到有效数据');
                        return;
                    }
                    
                    // 分析并显示结果，隐藏金额列
                    analyzeAndDisplayResults(data, true);
                } catch (error) {
                    hideLoading();
                    console.error('从Excel文件统计失败:', error);
                    alert('从Excel文件统计失败: ' + error.message);
                } finally {
                    // 清理文件输入框
                    document.body.removeChild(fileInput);
                }
            });
            
            // 触发文件选择
            fileInput.click();
        } catch (error) {
            console.error('从Excel文件统计失败:', error);
            alert('从Excel文件统计失败: ' + error.message);
        }
    }

    // 从页面提取数据
    function extractDataFromPage() {
        const data = [];
        
        try {
            // 优先查找金蝶云特定结构的表格
            const kdTables = document.querySelectorAll('.kd-table');
            
            kdTables.forEach(table => {
                // 查找表体
                const tbody = table.querySelector('.kd-table-body tbody');
                if (tbody) {
                    const rows = tbody.querySelectorAll('tr');
                    rows.forEach(row => {
                        const rowData = extractKDTableRowData(row);
                        if (rowData.salesRegion || rowData.settleStatus) {
                            data.push(rowData);
                        }
                    });
                }
            });
            
            // 如果没找到金蝶云特定表格，使用原有逻辑
            if (data.length === 0) {
                // 查找包含数据的表格 - 扩大搜索范围
                const gridViews = document.querySelectorAll('[id*="gridview"], [class*="grid"], [id*="Grid"], [class*="Grid"], .k-listview, .k-grid, .grid, table');
                
                gridViews.forEach(gridView => {
                    const rows = gridView.querySelectorAll('tr');
                    rows.forEach(row => {
                        const cells = row.querySelectorAll('td, th');
                        if (cells.length >= 5) { // 降低列数要求
                            const rowData = extractRowData(cells);
                            // 放宽数据完整性检查
                            if (rowData.salesRegion || rowData.settleStatus) {
                                data.push(rowData);
                            }
                        }
                    });
                });
                
                // 如果没找到数据，尝试查找所有表格
                if (data.length === 0) {
                    const allTables = document.querySelectorAll('table');
                    allTables.forEach(table => {
                        const rows = table.querySelectorAll('tr');
                        rows.forEach(row => {
                            const cells = row.querySelectorAll('td, th');
                            if (cells.length >= 5) {
                                const rowData = extractRowData(cells);
                                if (rowData.salesRegion || rowData.settleStatus) {
                                    data.push(rowData);
                                }
                            }
                        });
                    });
                }
            }
        } catch (error) {
            console.error('从页面提取数据失败:', error);
        }
        
        console.log('从页面提取到的数据:', data);
        return data;
    }

    // 提取金蝶云特定表格行数据
    function extractKDTableRowData(row) {
        const rowData = {
            salesRegion: '',
            settleStatus: '',
            amount: 0,
            billNo: ''
        };
        
        // 获取所有单元格
        const cells = row.querySelectorAll('td');
        
        // 遍历单元格，根据data-code属性识别字段
        cells.forEach(cell => {
            const dataCode = cell.getAttribute('data-code');
            const cellText = cell.textContent.trim();
            
            switch (dataCode) {
                case 'ghg_salesdept_name':
                    // 销售区域
                    rowData.salesRegion = cellText;
                    break;
                case 'ghg_shopname':
                    // 店铺名称（如果销售区域为空，可以作为备选）
                    if (!rowData.salesRegion) {
                        rowData.salesRegion = cellText;
                    }
                    break;
                case 'settlestatus':
                    // 核销状态
                    rowData.settleStatus = cellText;
                    break;
                case 'billno':
                    // 单据编号
                    rowData.billNo = cellText;
                    break;
                case 'pricetaxtotal':
                    // 含税总金额
                    const amount = parseFloat(cellText.replace(/[￥¥,，]/g, ''));
                    if (!isNaN(amount)) {
                        rowData.amount = amount;
                    }
                    break;
            }
        });
        
        return rowData;
    }

    // 提取行数据
    function extractRowData(cells) {
        const rowData = {
            salesRegion: '',
            settleStatus: '',
            amount: 0,
            billNo: ''
        };
        
        // 收集所有单元格文本用于后续分析
        const cellTexts = Array.from(cells).map(cell => cell.textContent.trim());
        
        // 遍历单元格，根据内容识别字段
        cells.forEach((cell, index) => {
            const cellText = cellTexts[index];
            
            // 识别销售区域 - 更智能的识别
            if (!rowData.salesRegion && (containsKeywords(cellText, ['销售区域', 'ghg_salesdept_name']) ||
                (cellText.includes('区') || cellText.includes('组') || cellText.includes('部')) &&
                cellText.length <= 10 && cellText.length >= 2)) {
                rowData.salesRegion = cellText;
            }
            
            // 识别核销状态 - 更全面的关键词
            if (!rowData.settleStatus && containsKeywords(cellText, ['核销状态', 'settlestatus', '未核销', '部分核销', '全部核销', '勾稽', '结算'])) {
                rowData.settleStatus = cellText;
            }
            
            // 识别单据编号 - 更宽松的匹配
            if (!rowData.billNo && (cellText.startsWith('AP-') || cellText.startsWith('AR-') ||
                /^([A-Z]{2,3}-\d+)/.test(cellText))) {
                rowData.billNo = cellText;
            }
            
            // 识别金额 - 更准确的匹配
            if (cellText.includes('￥') || cellText.includes('¥') ||
                /^\d{1,12}(\.\d{1,2})?$/.test(cellText.replace(/[,，]/g, '')) ||
                /^(\d{1,3}(,\d{3})*(\.\d{2})?|\d+(.\d{2})?)$/.test(cellText)) {
                const amountText = cellText.replace(/[￥¥,，]/g, '');
                const amount = parseFloat(amountText);
                if (!isNaN(amount) && amount > 0) {
                    // 优先选择较大的金额作为应付金额
                    if (amount > rowData.amount) {
                        rowData.amount = amount;
                    }
                }
            }
        });
        
        // 如果没有找到销售区域，尝试从整行文本中提取
        if (!rowData.salesRegion) {
            const rowText = cellTexts.join(' ');
            // 查找类似"销售区域: 华东区"的模式
            const regionMatch = rowText.match(/(?:销售区域|区域)[:：]?\s*([^:\s]{2,10}(?:区|部|组))/);
            if (regionMatch) {
                rowData.salesRegion = regionMatch[1];
            }
        }
        
        // 如果没有找到核销状态，尝试从整行文本中提取
        if (!rowData.settleStatus) {
            const rowText = cellTexts.join(' ');
            const statusMatch = rowText.match(/(?:核销状态|状态)[:：]?\s*([^:\s]{2,8}(?:核销|勾稽|结算))/);
            if (statusMatch) {
                rowData.settleStatus = statusMatch[1];
            }
        }
        
        return rowData;
    }

    // 检查是否包含关键词
    function containsKeywords(text, keywords) {
        return keywords.some(keyword => text.toLowerCase().includes(keyword.toLowerCase()));
    }

    // 从API获取数据
    async function fetchDataFromAPI() {
        const data = [];
        
        try {
            console.log('开始API数据获取...');
            
            // 获取当前页面的请求参数
            const csrfToken = getCSRFToken();
            const userId = getUserId();
            const pageId = getPageId();
            
            console.log('获取到的参数:', { csrfToken, userId, pageId });
            
            // 如果关键参数缺失，直接返回空数组
            if (!csrfToken || !userId) {
                console.warn('缺少必要的认证参数');
                return data;
            }
            
            // 构造请求
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('/form/')[0];
            const appId = getQueryParam('appId') || 'ap';
            const formId = getQueryParam('formId') || 'ap_finapbill';
            
            // 尝试多种可能的API端点
            const endpoints = [
                `${baseUrl}/form/batchInvokeAction.do?appId=${appId}&f=${formId}&ac=refresh`,
                `${baseUrl}/api/bill/list.do`,
                `${baseUrl}/form/data.do`
            ];
            
            const headers = {
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'ajax': 'true',
                'content-type': 'application/x-www-form-urlencoded;charset=utf-8;',
                'kd-csrf-token': csrfToken,
                'userid': userId,
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin'
            };
            
            // 构造请求体
            const bodyParams = {
                pageId: pageId || '',
                appId: appId,
                formId: formId,
                params: JSON.stringify([{
                    "key": "toolbarap",
                    "methodName": "itemClick",
                    "args": ["tblrefresh", "refresh"],
                    "postData": [{}, []]
                }])
            };
            
            // 尝试每个端点
            for (const url of endpoints) {
                try {
                    console.log('尝试API端点:', url);
                    
                    const body = new URLSearchParams(bodyParams);
                    
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: headers,
                        body: body,
                        credentials: 'include'
                    });
                    
                    console.log('API响应状态:', response.status);
                    
                    if (response.ok) {
                        const result = await response.json();
                        console.log('API响应数据:', result);
                        
                        const parsedData = parseAPIResponse(result);
                        if (parsedData.length > 0) {
                            return parsedData;
                        }
                    }
                } catch (endpointError) {
                    console.warn('端点请求失败:', url, endpointError);
                }
            }
            
            console.warn('所有API端点都失败了');
            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            return data; // 不抛出错误，而是返回空数组
        }
    }

    // 解析API响应数据
    function parseAPIResponse(response) {
        const data = [];
        
        try {
            console.log('开始解析API响应:', response);
            
            // 查找数据部分 - 更灵活的查找方式
            let billList = null;
            
            // 遍历响应查找数据
            if (Array.isArray(response)) {
                billList = response.find(item => item.p && item.p.data && item.p.data.rows);
            } else if (response.p && response.p.data && response.p.data.rows) {
                billList = response;
            } else if (response.data && response.data.rows) {
                billList = { p: { data: response.data } };
            } else if (response.rows) {
                billList = { p: { data: response } };
            }
            
            if (billList && billList.p && billList.p.data && billList.p.data.rows) {
                const rows = billList.p.data.rows;
                const dataIndex = billList.p.data.dataindex || {};
                
                console.log('找到数据行数:', rows.length);
                
                // 找到关键字段的索引 - 更灵活的查找方式
                let salesRegionIndex = Object.keys(dataIndex).find(key => dataIndex[key] === 'ghg_salesdept_name');
                let settleStatusIndex = Object.keys(dataIndex).find(key => dataIndex[key] === 'settlestatus');
                let amountIndex = Object.keys(dataIndex).find(key => dataIndex[key] === 'pricetaxtotal');
                let billNoIndex = Object.keys(dataIndex).find(key => dataIndex[key] === 'billno');
                
                // 如果没找到标准字段名，尝试其他可能的字段名
                if (!salesRegionIndex) {
                    salesRegionIndex = Object.keys(dataIndex).find(key =>
                        dataIndex[key].includes('sales') && dataIndex[key].includes('dept') ||
                        dataIndex[key].includes('销售') && dataIndex[key].includes('区域')
                    );
                }
                
                if (!settleStatusIndex) {
                    settleStatusIndex = Object.keys(dataIndex).find(key =>
                        dataIndex[key].includes('settle') || dataIndex[key].includes('核销') ||
                        dataIndex[key].includes('status')
                    );
                }
                
                if (!amountIndex) {
                    amountIndex = Object.keys(dataIndex).find(key =>
                        dataIndex[key].includes('price') || dataIndex[key].includes('amount') ||
                        dataIndex[key].includes('金额') || dataIndex[key].includes('总价')
                    );
                }
                
                console.log('字段索引:', { salesRegionIndex, settleStatusIndex, amountIndex, billNoIndex });
                
                rows.forEach((row, rowIndex) => {
                    try {
                        const salesRegion = salesRegionIndex ? row[parseInt(salesRegionIndex)] : '';
                        const settleStatus = settleStatusIndex ? row[parseInt(settleStatusIndex)] : '';
                        const amount = amountIndex ? parseFloat(row[parseInt(amountIndex)]) || 0 : 0;
                        const billNo = billNoIndex ? row[parseInt(billNoIndex)] : '';
                        
                        // 如果标准字段没找到，尝试从行数据中查找
                        if (!salesRegion) {
                            // 遍历行数据查找可能的销售区域
                            for (let i = 0; i < row.length; i++) {
                                const cellValue = row[i];
                                if (typeof cellValue === 'string' &&
                                    (cellValue.includes('区') || cellValue.includes('部') || cellValue.includes('组')) &&
                                    cellValue.length <= 10) {
                                    salesRegion = cellValue;
                                    break;
                                }
                            }
                        }
                        
                        if (!settleStatus) {
                            // 遍历行数据查找可能的核销状态
                            for (let i = 0; i < row.length; i++) {
                                const cellValue = row[i];
                                if (typeof cellValue === 'string' &&
                                    (cellValue.includes('核销') || cellValue.includes('未') || cellValue.includes('部分'))) {
                                    settleStatus = cellValue;
                                    break;
                                }
                            }
                        }
                        
                        if (salesRegion || settleStatus) {
                            data.push({
                                salesRegion: salesRegion || '',
                                settleStatus: settleStatus || '',
                                amount: amount,
                                billNo: billNo || ''
                            });
                        }
                    } catch (rowError) {
                        console.warn('解析行数据失败:', rowIndex, rowError);
                    }
                });
            } else {
                console.warn('未找到有效的数据结构');
                console.log('响应结构:', Object.keys(response || {}));
            }
        } catch (error) {
            console.error('解析API响应失败:', error);
        }
        
        console.log('解析后的数据:', data);
        return data;
    }

    // 分析数据并显示结果
    function analyzeAndDisplayResults(data, hideAmountColumns = false) {
        hideLoading();
        
        if (data.length === 0) {
            alert('未找到数据');
            return;
        }
        
        // 统计各销售区域未核销数量和金额
        const stats = {};
        let totalUnverified = 0;
        let totalAmount = 0;
        
        data.forEach(item => {
            // 检查是否为未核销状态
            if (isUnverifiedStatus(item.settleStatus)) {
                const region = item.salesRegion;
                if (!stats[region]) {
                    stats[region] = { count: 0, amount: 0 };
                }
                stats[region].count++;
                stats[region].amount += item.amount;
                totalUnverified++;
                totalAmount += item.amount;
            }
        });
        
        // 转换为数组并排序
        const results = Object.entries(stats)
            .map(([region, data]) => ({
                region,
                count: data.count,
                amount: data.amount,
                percentage: totalUnverified > 0 ? ((data.count / totalUnverified) * 100).toFixed(2) : 0,
                amountPercentage: totalAmount > 0 ? ((data.amount / totalAmount) * 100).toFixed(2) : 0
            }))
            .sort((a, b) => b.count - a.count);
        
        // 显示结果
        displayResults(results, totalUnverified, totalAmount, hideAmountColumns);
    }

    // 判断是否为未核销状态
    function isUnverifiedStatus(status) {
        if (!status) return false;
        
        const statusStr = status.toString().toLowerCase();
        const unverifiedKeywords = ['未核销', 'unsettle', '未勾稽', '未结算', '未核销'];
        return unverifiedKeywords.some(keyword => statusStr.includes(keyword.toLowerCase()));
    }

    // 显示结果
    function displayResults(results, totalCount, totalAmount, hideAmountColumns = false) {
        // 移除已存在的面板
        const existingPanel = document.getElementById('stat-results-panel');
        if (existingPanel) {
            existingPanel.remove();
        }
        
        // 创建结果面板
        const panel = document.createElement('div');
        panel.id = 'stat-results-panel';
        panel.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10000;
            width: ${hideAmountColumns ? '500px' : '800px'};
            max-height: 80vh;
            background: white;
            border: 2px solid #4CAF50;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            padding: 25px;
            overflow-y: auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        // 标题
        const title = document.createElement('h2');
        title.textContent = `📊 未核销统计结果`;
        title.style.cssText = 'margin-top: 0; color: #333; text-align: center; font-size: 24px;';
        
        // 总计信息
        const summary = document.createElement('div');
        summary.innerHTML = `
            <div style="display: flex; justify-content: space-around; margin-bottom: 20px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="text-align: center;">
                    <div style="font-size: 14px; color: #666;">未核销单据数</div>
                    <div style="font-size: 24px; font-weight: bold; color: #e74c3c;">${totalCount}</div>
                </div>
                ${!hideAmountColumns ? `<div style="text-align: center;">
                    <div style="font-size: 14px; color: #666;">未核销总金额</div>
                    <div style="font-size: 24px; font-weight: bold; color: #e67e22;">￥${totalAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}</div>
                </div>` : ''}
            </div>
        `;
        
        // 结果表格
        const table = document.createElement('table');
        table.style.cssText = `
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        `;
        
        // 表头
        const header = document.createElement('thead');
        if (hideAmountColumns) {
            header.innerHTML = `
                <tr style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: left; font-weight: 600;">销售区域</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: 600;">未核销数量</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: 600;">占比</th>
                </tr>
            `;
        } else {
            header.innerHTML = `
                <tr style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: left; font-weight: 600;">销售区域</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: 600;">未核销数量</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: 600;">占比</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: 600;">未核销金额</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: 600;">金额占比</th>
                </tr>
            `;
        }
        
        // 表体
        const tbody = document.createElement('tbody');
        results.forEach((item, index) => {
            const row = document.createElement('tr');
            row.style.backgroundColor = index % 2 === 0 ? '#fff' : '#f8f9fa';
            if (hideAmountColumns) {
                row.innerHTML = `
                    <td style="border: 1px solid #ddd; padding: 10px; font-weight: 500;">${item.region}</td>
                    <td style="border: 1px solid #ddd; padding: 10px; text-align: center; font-weight: bold; color: #e74c3c;">${item.count}</td>
                    <td style="border: 1px solid #ddd; padding: 10px; text-align: center; color: #666;">${item.percentage}%</td>
                `;
            } else {
                row.innerHTML = `
                    <td style="border: 1px solid #ddd; padding: 10px; font-weight: 500;">${item.region}</td>
                    <td style="border: 1px solid #ddd; padding: 10px; text-align: center; font-weight: bold; color: #e74c3c;">${item.count}</td>
                    <td style="border: 1px solid #ddd; padding: 10px; text-align: center; color: #666;">${item.percentage}%</td>
                    <td style="border: 1px solid #ddd; padding: 10px; text-align: right; font-weight: 500; color: #e67e22;">￥${item.amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}</td>
                    <td style="border: 1px solid #ddd; padding: 10px; text-align: center; color: #666;">${item.amountPercentage}%</td>
                `;
            }
            tbody.appendChild(row);
        });
        
        // 关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.textContent = '✕ 关闭';
        closeBtn.style.cssText = `
            margin-top: 20px;
            padding: 10px 20px;
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            float: right;
            transition: all 0.3s ease;
        `;
        closeBtn.onmouseenter = function() {
            this.style.transform = 'scale(1.05)';
        };
        closeBtn.onmouseleave = function() {
            this.style.transform = 'scale(1)';
        };
        closeBtn.onclick = () => panel.remove();
        
        // 导出按钮
        const exportBtn = document.createElement('button');
        exportBtn.textContent = '💾 导出Excel';
        exportBtn.style.cssText = `
            margin-top: 20px;
            margin-right: 10px;
            padding: 10px 20px;
            background: linear-gradient(135deg, #28a745 0%, #218838 100%);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            float: right;
            transition: all 0.3s ease;
        `;
        exportBtn.onmouseenter = function() {
            this.style.transform = 'scale(1.05)';
        };
        exportBtn.onmouseleave = function() {
            this.style.transform = 'scale(1)';
        };
        exportBtn.onclick = () => exportToExcel(results, totalCount, totalAmount, hideAmountColumns);
        
        // 组装面板
        table.appendChild(header);
        table.appendChild(tbody);
        panel.appendChild(title);
        panel.appendChild(summary);
        panel.appendChild(table);
        panel.appendChild(exportBtn);
        panel.appendChild(closeBtn);
        
        // 添加到页面
        document.body.appendChild(panel);
        
        // 点击外部关闭
        panel.addEventListener('click', (e) => {
            if (e.target === panel) {
                panel.remove();
            }
        });
    }

    // 导出到Excel
    function exportToExcel(results, totalCount, totalAmount, hideAmountColumns = false) {
        try {
            // 创建CSV内容
            let csvContent = '\uFEFF'; // BOM for UTF-8
            if (hideAmountColumns) {
                csvContent += '销售区域,未核销数量,占比(%)\n';
                
                results.forEach(item => {
                    csvContent += `"${item.region}",${item.count},${item.percentage}\n`;
                });
                
                // 添加总计行
                csvContent += `总计,${totalCount},100.00\n`;
            } else {
                csvContent += '销售区域,未核销数量,占比(%),未核销金额,金额占比(%)\n';
                
                results.forEach(item => {
                    csvContent += `"${item.region}",${item.count},${item.percentage},"${item.amount.toFixed(2)}",${item.amountPercentage}\n`;
                });
                
                // 添加总计行
                csvContent += `总计,${totalCount},100.00,"${totalAmount.toFixed(2)}",100.00\n`;
            }
            
            // 创建下载链接
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.setAttribute('href', url);
            link.setAttribute('download', `金蝶云未核销统计_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            alert('导出成功！');
        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败: ' + error.message);
        }
    }

    // 显示加载状态
    function showLoading() {
        // 移除已存在的加载状态
        const existingLoading = document.getElementById('stat-loading');
        if (existingLoading) {
            existingLoading.remove();
        }
        
        const loading = document.createElement('div');
        loading.id = 'stat-loading';
        loading.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <div class="spinner" style="
                    width: 20px;
                    height: 20px;
                    border: 3px solid #f3f3f3;
                    border-top: 3px solid #4CAF50;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                "></div>
                <span>正在统计中...</span>
            </div>
        `;
        loading.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10001;
            padding: 20px 40px;
            background: rgba(0,0,0,0.8);
            color: white;
            border-radius: 8px;
            font-size: 16px;
        `;
        
        // 添加旋转动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(loading);
    }

    // 隐藏加载状态
    function hideLoading() {
        const loading = document.getElementById('stat-loading');
        if (loading) {
            loading.remove();
        }
    }

    // 获取CSRF Token
    function getCSRFToken() {
        // 从meta标签获取
        const metaToken = document.querySelector('meta[name="kd-csrf-token"]');
        if (metaToken) {
            return metaToken.content;
        }
        
        // 从cookie获取
        const cookieToken = getCookie('kd_csrf_token');
        if (cookieToken) {
            return cookieToken;
        }
        
        // 从localStorage获取
        const localToken = localStorage.getItem('kd_csrf_token');
        if (localToken) {
            return localToken;
        }
        
        // 从请求头获取
        const headerToken = document.querySelector('[kd-csrf-token]');
        if (headerToken) {
            return headerToken.getAttribute('kd-csrf-token');
        }
        
        return '';
    }

    // 获取用户ID
    function getUserId() {
        // 从URL参数获取
        const urlUserId = getQueryParam('userid');
        if (urlUserId) {
            return urlUserId;
        }
        
        // 从localStorage获取
        const localUserId = localStorage.getItem('userid');
        if (localUserId) {
            return localUserId;
        }
        
        // 从cookie获取
        const cookieUserId = getCookie('userid');
        if (cookieUserId) {
            return cookieUserId;
        }
        
        return '';
    }

    // 获取页面ID
    function getPageId() {
        // 从隐藏字段获取
        const pageIdInput = document.querySelector('input[name="pageId"]');
        if (pageIdInput) {
            return pageIdInput.value;
        }
        
        // 从URL参数获取
        const urlPageId = getQueryParam('pageId');
        if (urlPageId) {
            return urlPageId;
        }
        
        // 从data属性获取
        const dataPageId = document.querySelector('[data-pageid]');
        if (dataPageId) {
            return dataPageId.getAttribute('data-pageid');
        }
        
        return '';
    }

    // 获取Cookie
    function getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return '';
    }

    // 获取URL参数
    function getQueryParam(param) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(param);
    }
})();