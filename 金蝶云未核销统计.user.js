    // ==UserScript==
    // @name         金蝶云未核销统计
    // @namespace    http://tampermonkey.net/
    // @version      1.2
    // @description  统计金蝶云系统中各销售区域的未核销应付单数量
    // <AUTHOR>
    // @match        https://*.kdyun.com/*
    // @match        https://*.kdgalaxy.com/*
    // @grant        none
    // ==/UserScript==

    (function() {
        'use strict';

        // 存储拦截到的xlsx数据
        let interceptedXlsxData = null;

        // 拦截下载请求 - 增强版本
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;
        const originalFetch = window.fetch;
        
        // 拦截 XMLHttpRequest
        XMLHttpRequest.prototype.open = function(method, url) {
            this._url = url;
            this._method = method;
            console.log('XHR请求:', method, url);
            return originalOpen.apply(this, arguments);
        };
        
        XMLHttpRequest.prototype.send = function() {
            // 检查是否为下载请求 - 修改判断逻辑
            if (this._url && this._url.includes('attachment/download.do')) {
                console.log('检测到可能的下载请求:', this._url);
                
                // 设置响应类型为arraybuffer以获取二进制数据
                if (!this.responseType) {
                    this.responseType = 'arraybuffer';
                }
                
                // 监听请求完成
                this.addEventListener('load', function() {
                    console.log('下载请求完成，状态:', this.status, '响应类型:', this.responseType, '响应大小:', this.response ? this.response.byteLength : 0);
                    
                    if (this.status === 200 && this.response) {
                        try {
                            // 检查响应内容是否为Excel文件
                            const arrayBuffer = this.response;
                            const uint8Array = new Uint8Array(arrayBuffer);
                            
                            // 检查Excel文件头 (PK开头表示zip格式，xlsx是zip格式)
                            if (uint8Array.length > 4 && 
                                uint8Array[0] === 0x50 && uint8Array[1] === 0x4B) {
                                console.log('拦截到Excel文件:', this._url);
                                
                                // 创建Blob对象
                                const blob = new Blob([arrayBuffer], { 
                                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
                                });
                                
                                // 自动解析并统计
                                parseAndAnalyzeBlob(blob);
                            } else {
                                console.log('响应不是Excel文件格式');
                            }
                        } catch (error) {
                            console.error('拦截数据处理失败:', error);
                        }
                    }
                });
                
                // 监听错误
                this.addEventListener('error', function() {
                    console.error('下载请求失败:', this._url);
                });
            }
            return originalSend.call(this);
        };
        
        // 拦截 fetch API
        window.fetch = function(...args) {
            const url = args[0];
            console.log('Fetch请求:', url);
            
            if (typeof url === 'string' && url.includes('attachment/download.do')) {
                console.log('检测到fetch下载请求:', url);
                
                return originalFetch.apply(this, args).then(response => {
                    if (response.ok) {
                        console.log('Fetch下载成功，开始处理响应');
                        
                        // 克隆响应以避免消费原始响应
                        const clonedResponse = response.clone();
                        
                        clonedResponse.arrayBuffer().then(arrayBuffer => {
                            try {
                                const uint8Array = new Uint8Array(arrayBuffer);
                                
                                // 检查Excel文件头
                                if (uint8Array.length > 4 && 
                                    uint8Array[0] === 0x50 && uint8Array[1] === 0x4B) {
                                    console.log('Fetch拦截到Excel文件:', url);
                                    
                                    const blob = new Blob([arrayBuffer], { 
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
                                    });
                                    
                                    parseAndAnalyzeBlob(blob);
                                }
                            } catch (error) {
                                console.error('Fetch拦截数据处理失败:', error);
                            }
                        }).catch(error => {
                            console.error('处理fetch响应失败:', error);
                        });
                    }
                    
                    return response;
                });
            }
            
            return originalFetch.apply(this, args);
        };
        // 简化的xlsx解析函数
        function parseXlsxData(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        // 使用SheetJS库解析xlsx文件
                        const data = new Uint8Array(e.target.result);
                        const workbook = window.XLSX.read(data, { type: 'array' });
                        
                        // 获取第一个工作表
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        
                        // 转换为JSON格式
                        const jsonData = window.XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                        resolve(jsonData);
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = function(error) {
                    reject(error);
                };
                reader.readAsArrayBuffer(file);
            });
        }

        // 解析Blob并自动分析 - 增强版本
        function parseAndAnalyzeBlob(blob) {
            console.log('开始解析Blob数据，大小:', blob.size, '类型:', blob.type);
            
            if (!window.XLSX) {
                console.warn('SheetJS库未加载，无法解析Excel文件');
                alert('Excel解析库未加载完成，请稍后重试或刷新页面');
                return;
            }
            
            // 显示加载状态
            showLoading();
            
            // 解析Blob
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    console.log('FileReader读取完成，数据大小:', e.target.result.byteLength);
                    
                    // 使用SheetJS库解析xlsx文件
                    const data = new Uint8Array(e.target.result);
                    console.log('准备解析Excel数据，前4个字节:', Array.from(data.slice(0, 4)).map(b => b.toString(16)));
                    
                    const workbook = window.XLSX.read(data, { type: 'array' });
                    console.log('Excel解析成功，工作表:', workbook.SheetNames);
                    
                    // 获取第一个工作表
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    
                    // 转换为JSON格式
                    const jsonData = window.XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    console.log('转换为JSON成功，行数:', jsonData.length);
                    console.log('前3行数据:', jsonData.slice(0, 3));
                    
                    // 提取数据
                    const extractedData = extractDataFromExcel(jsonData);
                    console.log('提取数据完成，有效数据行数:', extractedData.length);
                    
                    if (extractedData.length === 0) {
                        hideLoading();
                        alert('未从Excel文件中找到有效数据，请检查文件格式');
                        return;
                    }
                    
                    console.log('开始分析并显示结果');
                    // 自动分析并显示结果，隐藏金额列
                    analyzeAndDisplayResults(extractedData, true);
                } catch (error) {
                    hideLoading();
                    console.error('解析Excel文件失败:', error);
                    alert('解析Excel文件失败: ' + error.message);
                }
            };
            
            reader.onerror = function(error) {
                hideLoading();
                console.error('读取Excel文件失败:', error);
                alert('读取Excel文件失败: ' + error.message);
            };
            
            reader.readAsArrayBuffer(blob);
        }

        // 从Excel数据中提取统计信息
        function extractDataFromExcel(excelData) {
            if (!excelData || excelData.length === 0) return [];
            
            const data = [];
            // 假设第一行是标题行
            const headers = excelData[0];
            
            // 查找关键列的索引
            let salesRegionIndex = -1;
            let settleStatusIndex = -1;
            let amountIndex = -1;
            let billNoIndex = -1;
            
            // 查找标题行中的关键字段 - 更全面的匹配
            for (let i = 0; i < headers.length; i++) {
                const header = headers[i].toString().toLowerCase();
                if (header.includes('销售区域') || header.includes('ghg_salesdept_name') ||
                    header.includes('部门') || header.includes('区域')) {
                    salesRegionIndex = i;
                } else if (header.includes('核销状态') || header.includes('settlestatus') ||
                        header.includes('状态') || header.includes('勾稽状态')) {
                    settleStatusIndex = i;
                } else if (header.includes('含税价') || header.includes('价税合计') ||
                        header.includes('金额') || header.includes('总价') ||
                        header.includes('pricetaxtotal')) {
                    amountIndex = i;
                } else if (header.includes('单据编号') || header.includes('billno') ||
                        header.includes('编号') || header.includes('单号')) {
                    billNoIndex = i;
                }
            }
            
            // 解析数据行
            for (let i = 1; i < excelData.length; i++) {
                const row = excelData[i];
                if (!row || row.length === 0) continue;
                
                // 获取单元格值并处理空值
                const getCellValue = (index) => {
                    if (index < 0 || index >= row.length) return '';
                    const value = row[index];
                    return value !== null && value !== undefined ? value.toString().trim() : '';
                };
                
                const rowData = {
                    salesRegion: getCellValue(salesRegionIndex),
                    settleStatus: getCellValue(settleStatusIndex),
                    amount: amountIndex >= 0 ? parseFloat(getCellValue(amountIndex).replace(/[￥¥,，]/g, '')) || 0 : 0,
                    billNo: getCellValue(billNoIndex)
                };
                
                // 只有当有销售区域或核销状态时才添加数据
                if (rowData.salesRegion || rowData.settleStatus) {
                    data.push(rowData);
                }
            }
            
            return data;
        }

        // 添加SheetJS库
        function addSheetJS() {
            // 检查是否已经加载了SheetJS
            if (window.XLSX) return;
            
            // 动态加载SheetJS库
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js';
            script.onload = function() {
                console.log('SheetJS库加载成功');
            };
            script.onerror = function() {
                console.error('SheetJS库加载失败');
            };
            document.head.appendChild(script);
        }

        // 等待页面加载完成
        window.addEventListener('load', function() {
            console.log('金蝶云未核销统计脚本已加载');
            console.log('自动拦截功能已启动，正在监听下载请求...');
            
            // 创建统计按钮
            createStatButton();
            // 添加SheetJS库
            addSheetJS();
        });

        // 创建统计按钮
        function createStatButton() {
            // 查找指定位置添加按钮 - 新的目标元素
            const targetDiv = document.getElementById('orgcombofield');
            
            if (targetDiv) {
                // 创建按钮容器
                const buttonContainer = document.createElement('div');
                buttonContainer.style.cssText = `
                    display: inline-block;
                    margin-left: 10px;
                    vertical-align: middle;
                `;
                
                // 创建从Excel文件统计按钮
                const excelStatButton = document.createElement('button');
                excelStatButton.id = 'stat-unverified-excel-btn';
                excelStatButton.textContent = '📊 从Excel文件统计';
                excelStatButton.style.cssText = `
                    padding: 5px 10px;
                    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    font-weight: bold;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                    transition: all 0.3s ease;
                `;
                
                excelStatButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.boxShadow = '0 3px 10px rgba(0,0,0,0.3)';
                });
                
                excelStatButton.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
                });
                
                excelStatButton.addEventListener('click', analyzeUnverifiedDataFromExcel);
                
                // 创建自动拦截统计按钮
                const autoInterceptButton = document.createElement('button');
                autoInterceptButton.id = 'auto-intercept-stat-btn';
                autoInterceptButton.textContent = '📊 自动拦截统计';
                autoInterceptButton.style.cssText = `
                    padding: 5px 10px;
                    background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    font-weight: bold;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                    transition: all 0.3s ease;
                    margin-left: 5px;
                `;
                
                autoInterceptButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.boxShadow = '0 3px 10px rgba(0,0,0,0.3)';
                });
                
                autoInterceptButton.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
                });
                
                autoInterceptButton.addEventListener('click', function() {
                    // 更详细的提示信息
                    alert('自动拦截功能已激活！\n\n请按以下步骤操作：\n1. 在金蝶云系统中点击"导出Excel"按钮\n2. 系统将自动拦截并解析Excel文件\n3. 统计结果会自动弹出\n\n注意：请保持页面不要刷新，确保拦截功能正常工作');
                    console.log('用户激活了自动拦截功能，正在监听下载请求...');
                });
                
                // 将按钮添加到容器
                buttonContainer.appendChild(excelStatButton);
                buttonContainer.appendChild(autoInterceptButton);
                
                // 将容器插入到目标div后面
                targetDiv.parentNode.insertBefore(buttonContainer, targetDiv.nextSibling);
            } else {
                // 如果找不到指定位置，回退到原来的方式
                const toolbar = document.querySelector('[id*="toolbar"]') || document.querySelector('.toolbar') || document.body;
                
                // 添加从Excel文件统计按钮
                const excelButton = document.createElement('button');
                excelButton.id = 'stat-unverified-excel-btn';
                excelButton.textContent = '📊 从Excel文件统计';
                excelButton.style.cssText = `
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    z-index: 9999;
                    padding: 10px 15px;
                    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: bold;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                    transition: all 0.3s ease;
                `;
                
                excelButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.boxShadow = '0 6px 20px rgba(0,0,0,0.3)';
                });
                
                excelButton.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
                });
                
                excelButton.addEventListener('click', analyzeUnverifiedDataFromExcel);
                document.body.appendChild(excelButton);
                
                // 添加自动拦截统计按钮
                const autoInterceptButton = document.createElement('button');
                autoInterceptButton.id = 'auto-intercept-stat-btn';
                autoInterceptButton.textContent = '📊 自动拦截统计';
                autoInterceptButton.style.cssText = `
                    position: fixed;
                    top: 60px;
                    right: 10px;
                    z-index: 9999;
                    padding: 10px 15px;
                    background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: bold;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                    transition: all 0.3s ease;
                `;
                
                autoInterceptButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.boxShadow = '0 6px 20px rgba(0,0,0,0.3)';
                });
                
                autoInterceptButton.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
                });
                
                autoInterceptButton.addEventListener('click', function() {
                    // 更详细的提示信息
                    alert('自动拦截功能已激活！\n\n请按以下步骤操作：\n1. 在金蝶云系统中点击"导出Excel"按钮\n2. 系统将自动拦截并解析Excel文件\n3. 统计结果会自动弹出\n\n注意：请保持页面不要刷新，确保拦截功能正常工作');
                    console.log('用户激活了自动拦截功能，正在监听下载请求...');
                });
                
                document.body.appendChild(autoInterceptButton);
            }
        }


        // 从Excel文件分析未核销数据
        async function analyzeUnverifiedDataFromExcel() {
            try {
                // 检查SheetJS库是否已加载
                if (!window.XLSX) {
                    alert('Excel解析库未加载，请稍后重试');
                    return;
                }
                
                // 创建文件选择输入框
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = '.xlsx,.xls';
                fileInput.style.display = 'none';
                
                // 添加到页面
                document.body.appendChild(fileInput);
                
                // 设置文件选择事件
                fileInput.addEventListener('change', async function(event) {
                    const file = event.target.files[0];
                    if (!file) return;
                    
                    try {
                        showLoading();
                        
                        // 解析Excel文件
                        const excelData = await parseXlsxData(file);
                        
                        // 提取数据
                        const data = extractDataFromExcel(excelData);
                        
                        if (data.length === 0) {
                            hideLoading();
                            alert('未从Excel文件中找到有效数据');
                            return;
                        }
                        
                        // 分析并显示结果，隐藏金额列
                        analyzeAndDisplayResults(data, true);
                    } catch (error) {
                        hideLoading();
                        console.error('从Excel文件统计失败:', error);
                        alert('从Excel文件统计失败: ' + error.message);
                    } finally {
                        // 清理文件输入框
                        document.body.removeChild(fileInput);
                    }
                });
                
                // 触发文件选择
                fileInput.click();
            } catch (error) {
                console.error('从Excel文件统计失败:', error);
                alert('从Excel文件统计失败: ' + error.message);
            }
        }





        // 分析数据并显示结果
        function analyzeAndDisplayResults(data, hideAmountColumns = false) {
            hideLoading();
            
            if (data.length === 0) {
                alert('未找到数据');
                return;
            }
            
            // 统计各销售区域未核销数量和金额
            const stats = {};
            let totalUnverified = 0;
            let totalAmount = 0;
            
            data.forEach(item => {
                // 检查是否为未核销状态
                if (isUnverifiedStatus(item.settleStatus)) {
                    const region = item.salesRegion;
                    if (!stats[region]) {
                        stats[region] = { count: 0, amount: 0 };
                    }
                    stats[region].count++;
                    stats[region].amount += item.amount;
                    totalUnverified++;
                    totalAmount += item.amount;
                }
            });
            
            // 转换为数组并排序
            const results = Object.entries(stats)
                .map(([region, data]) => ({
                    region,
                    count: data.count,
                    amount: data.amount,
                    percentage: totalUnverified > 0 ? ((data.count / totalUnverified) * 100).toFixed(2) : 0,
                    amountPercentage: totalAmount > 0 ? ((data.amount / totalAmount) * 100).toFixed(2) : 0
                }))
                .sort((a, b) => b.count - a.count);
            
            // 显示结果
            displayResults(results, totalUnverified, totalAmount, hideAmountColumns);
        }

        // 判断是否为未核销状态
        function isUnverifiedStatus(status) {
            if (!status) return false;
            
            const statusStr = status.toString().toLowerCase();
            const unverifiedKeywords = ['未核销', 'unsettle', '未勾稽', '未结算', '未核销'];
            return unverifiedKeywords.some(keyword => statusStr.includes(keyword.toLowerCase()));
        }

        // 显示结果
        function displayResults(results, totalCount, totalAmount, hideAmountColumns = false) {
            // 移除已存在的面板
            const existingPanel = document.getElementById('stat-results-panel');
            if (existingPanel) {
                existingPanel.remove();
            }
            
            // 创建结果面板
            const panel = document.createElement('div');
            panel.id = 'stat-results-panel';
            panel.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 10000;
                width: ${hideAmountColumns ? '500px' : '800px'};
                max-height: 80vh;
                background: white;
                border: 2px solid #4CAF50;
                border-radius: 12px;
                box-shadow: 0 8px 30px rgba(0,0,0,0.3);
                padding: 25px;
                overflow-y: auto;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            `;
            
            // 标题
            const title = document.createElement('h2');
            title.textContent = `📊 未核销统计结果`;
            title.style.cssText = 'margin-top: 0; color: #333; text-align: center; font-size: 24px;';
            
            // 总计信息
            const summary = document.createElement('div');
            summary.innerHTML = `
                <div style="display: flex; justify-content: space-around; margin-bottom: 20px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <div style="text-align: center;">
                        <div style="font-size: 14px; color: #666;">未核销单据数</div>
                        <div style="font-size: 24px; font-weight: bold; color: #e74c3c;">${totalCount}</div>
                    </div>
                    ${!hideAmountColumns ? `<div style="text-align: center;">
                        <div style="font-size: 14px; color: #666;">未核销总金额</div>
                        <div style="font-size: 24px; font-weight: bold; color: #e67e22;">￥${totalAmount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}</div>
                    </div>` : ''}
                </div>
            `;
            
            // 结果表格
            const table = document.createElement('table');
            table.style.cssText = `
                width: 100%;
                border-collapse: collapse;
                margin-top: 15px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                border-radius: 8px;
                overflow: hidden;
            `;
            
            // 表头
            const header = document.createElement('thead');
            if (hideAmountColumns) {
                header.innerHTML = `
                    <tr style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: left; font-weight: 600;">销售区域</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: 600;">未核销数量</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: 600;">占比</th>
                    </tr>
                `;
            } else {
                header.innerHTML = `
                    <tr style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: left; font-weight: 600;">销售区域</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: 600;">未核销数量</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: 600;">占比</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: 600;">未核销金额</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: 600;">金额占比</th>
                    </tr>
                `;
            }
            
            // 表体
            const tbody = document.createElement('tbody');
            results.forEach((item, index) => {
                const row = document.createElement('tr');
                row.style.backgroundColor = index % 2 === 0 ? '#fff' : '#f8f9fa';
                if (hideAmountColumns) {
                    row.innerHTML = `
                        <td style="border: 1px solid #ddd; padding: 10px; font-weight: 500;">${item.region}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center; font-weight: bold; color: #e74c3c;">${item.count}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center; color: #666;">${item.percentage}%</td>
                    `;
                } else {
                    row.innerHTML = `
                        <td style="border: 1px solid #ddd; padding: 10px; font-weight: 500;">${item.region}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center; font-weight: bold; color: #e74c3c;">${item.count}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center; color: #666;">${item.percentage}%</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: right; font-weight: 500; color: #e67e22;">￥${item.amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center; color: #666;">${item.amountPercentage}%</td>
                    `;
                }
                tbody.appendChild(row);
            });
            
            // 关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.textContent = '✕ 关闭';
            closeBtn.style.cssText = `
                margin-top: 20px;
                padding: 10px 20px;
                background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                float: right;
                transition: all 0.3s ease;
            `;
            closeBtn.onmouseenter = function() {
                this.style.transform = 'scale(1.05)';
            };
            closeBtn.onmouseleave = function() {
                this.style.transform = 'scale(1)';
            };
            closeBtn.onclick = () => panel.remove();
            
            // 导出按钮
            const exportBtn = document.createElement('button');
            exportBtn.textContent = '💾 导出Excel';
            exportBtn.style.cssText = `
                margin-top: 20px;
                margin-right: 10px;
                padding: 10px 20px;
                background: linear-gradient(135deg, #28a745 0%, #218838 100%);
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                float: right;
                transition: all 0.3s ease;
            `;
            exportBtn.onmouseenter = function() {
                this.style.transform = 'scale(1.05)';
            };
            exportBtn.onmouseleave = function() {
                this.style.transform = 'scale(1)';
            };
            exportBtn.onclick = () => exportToExcel(results, totalCount, totalAmount, hideAmountColumns);
            
            // 组装面板
            table.appendChild(header);
            table.appendChild(tbody);
            panel.appendChild(title);
            panel.appendChild(summary);
            panel.appendChild(table);
            panel.appendChild(exportBtn);
            panel.appendChild(closeBtn);
            
            // 添加到页面
            document.body.appendChild(panel);
            
            // 点击外部关闭
            panel.addEventListener('click', (e) => {
                if (e.target === panel) {
                    panel.remove();
                }
            });
        }

        // 导出到Excel
        function exportToExcel(results, totalCount, totalAmount, hideAmountColumns = false) {
            try {
                // 创建CSV内容
                let csvContent = '\uFEFF'; // BOM for UTF-8
                if (hideAmountColumns) {
                    csvContent += '销售区域,未核销数量,占比(%)\n';
                    
                    results.forEach(item => {
                        csvContent += `"${item.region}",${item.count},${item.percentage}\n`;
                    });
                    
                    // 添加总计行
                    csvContent += `总计,${totalCount},100.00\n`;
                } else {
                    csvContent += '销售区域,未核销数量,占比(%),未核销金额,金额占比(%)\n';
                    
                    results.forEach(item => {
                        csvContent += `"${item.region}",${item.count},${item.percentage},"${item.amount.toFixed(2)}",${item.amountPercentage}\n`;
                    });
                    
                    // 添加总计行
                    csvContent += `总计,${totalCount},100.00,"${totalAmount.toFixed(2)}",100.00\n`;
                }
                
                // 创建下载链接
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.setAttribute('href', url);
                link.setAttribute('download', `金蝶云未核销统计_${new Date().toISOString().slice(0, 10)}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                alert('导出成功！');
            } catch (error) {
                console.error('导出失败:', error);
                alert('导出失败: ' + error.message);
            }
        }

        // 显示加载状态
        function showLoading() {
            // 移除已存在的加载状态
            const existingLoading = document.getElementById('stat-loading');
            if (existingLoading) {
                existingLoading.remove();
            }
            
            const loading = document.createElement('div');
            loading.id = 'stat-loading';
            loading.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <div class="spinner" style="
                        width: 20px;
                        height: 20px;
                        border: 3px solid #f3f3f3;
                        border-top: 3px solid #4CAF50;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    "></div>
                    <span>正在统计中...</span>
                </div>
            `;
            loading.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 10001;
                padding: 20px 40px;
                background: rgba(0,0,0,0.8);
                color: white;
                border-radius: 8px;
                font-size: 16px;
            `;
            
            // 添加旋转动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
            
            document.body.appendChild(loading);
        }

        // 隐藏加载状态
        function hideLoading() {
            const loading = document.getElementById('stat-loading');
            if (loading) {
                loading.remove();
            }
        }

    })();