// ==UserScript==
// @name         金蝶云批量统计分析
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  批量分析金蝶云导出的多个Excel文件，按销售区域统计各种业务状态数据
// <AUTHOR>
// @match        https://*.kdyun.com/*
// @match        https://*.kdgalaxy.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 存储所有文件的统计结果
    let allStatistics = {};
    let processedFiles = 0;
    let totalFiles = 0;

    // 添加SheetJS库
    function addSheetJS() {
        if (window.XLSX) return;
        
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js';
        script.onload = function() {
            console.log('SheetJS库加载成功');
        };
        script.onerror = function() {
            console.error('SheetJS库加载失败');
        };
        document.head.appendChild(script);
    }

    // 等待页面加载完成
    window.addEventListener('load', function() {
        console.log('金蝶云批量统计分析脚本已加载');
        createBatchAnalysisButton();
        addSheetJS();
    });

    // 创建批量分析按钮
    function createBatchAnalysisButton() {
        const targetDiv = document.getElementById('orgcombofield');
        
        if (targetDiv) {
            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = `
                display: inline-block;
                margin-left: 10px;
                vertical-align: middle;
            `;
            
            const batchButton = document.createElement('button');
            batchButton.id = 'batch-analysis-btn';
            batchButton.textContent = '📊 批量统计分析';
            batchButton.style.cssText = `
                padding: 5px 10px;
                background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                font-weight: bold;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            `;
            
            batchButton.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 3px 10px rgba(0,0,0,0.3)';
            });
            
            batchButton.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
            });
            
            batchButton.addEventListener('click', startBatchAnalysis);
            
            buttonContainer.appendChild(batchButton);
            targetDiv.parentNode.insertBefore(buttonContainer, targetDiv.nextSibling);
        } else {
            // 如果找不到指定位置，创建浮动按钮
            const floatButton = document.createElement('button');
            floatButton.id = 'batch-analysis-btn';
            floatButton.textContent = '📊 批量统计分析';
            floatButton.style.cssText = `
                position: fixed;
                top: 100px;
                right: 10px;
                z-index: 9999;
                padding: 10px 15px;
                background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            `;
            
            floatButton.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 6px 20px rgba(0,0,0,0.3)';
            });
            
            floatButton.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
            });
            
            floatButton.addEventListener('click', startBatchAnalysis);
            document.body.appendChild(floatButton);
        }
    }

    // 开始批量分析
    function startBatchAnalysis() {
        if (!window.XLSX) {
            alert('Excel解析库未加载，请稍后重试');
            return;
        }
        
        // 重置统计数据
        allStatistics = {};
        processedFiles = 0;
        totalFiles = 0;
        
        // 创建文件选择输入框，支持多文件选择
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.xlsx,.xls';
        fileInput.multiple = true; // 允许选择多个文件
        fileInput.style.display = 'none';
        
        document.body.appendChild(fileInput);
        
        fileInput.addEventListener('change', async function(event) {
            const files = Array.from(event.target.files);
            if (files.length === 0) return;
            
            totalFiles = files.length;
            showLoading(`正在处理 ${totalFiles} 个文件...`);
            
            try {
                // 处理所有文件
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    await processFile(file);
                    processedFiles++;
                    updateLoadingProgress(`正在处理文件 ${processedFiles}/${totalFiles}: ${file.name}`);
                }
                
                hideLoading();
                
                // 显示综合统计结果
                displayBatchResults();
                
            } catch (error) {
                hideLoading();
                console.error('批量分析失败:', error);
                alert('批量分析失败: ' + error.message);
            } finally {
                document.body.removeChild(fileInput);
            }
        });
        
        fileInput.click();
    }

    // 处理单个文件
    async function processFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    
                    // 根据文件名判断文件类型并处理
                    analyzeFileData(file.name, jsonData);
                    resolve();
                } catch (error) {
                    reject(error);
                }
            };
            
            reader.onerror = function(error) {
                reject(error);
            };
            
            reader.readAsArrayBuffer(file);
        });
    }

    // 分析文件数据
    function analyzeFileData(filename, data) {
        if (!data || data.length < 2) return;
        
        const headers = data[0];
        let fileType = '';
        let statusField = '';
        
        // 根据文件名确定文件类型和需要统计的状态字段
        if (filename.includes('采购订单暂存统计')) {
            fileType = '采购订单暂存';
            statusField = '单据状态';
        } else if (filename.includes('采购申请单统计')) {
            fileType = '采购申请单';
            statusField = '单据状态';
        } else if (filename.includes('每日各区域应付统计')) {
            fileType = '财务应付单';
            statusField = '核销状态';
        } else if (filename.includes('销售订单暂存统计')) {
            fileType = '销售订单暂存';
            statusField = '单据状态';
        } else if (filename.includes('原始订单区域未下推统计')) {
            fileType = '原始订单未下推';
            statusField = '单据状态';
        }
        
        // 查找列索引
        let regionIndex = -1;
        let statusIndex = -1;
        
        for (let i = 0; i < headers.length; i++) {
            const header = headers[i].toString().toLowerCase();
            if (header.includes('销售区域') && header.includes('名称')) {
                regionIndex = i;
            } else if (header.includes(statusField.toLowerCase()) || header === statusField) {
                statusIndex = i;
            }
        }
        
        // 统计数据
        for (let i = 1; i < data.length; i++) {
            const row = data[i];
            if (!row || row.length === 0) continue;
            
            const region = regionIndex >= 0 ? row[regionIndex] : '';
            const status = statusIndex >= 0 ? row[statusIndex] : '';
            
            if (region) {
                if (!allStatistics[region]) {
                    allStatistics[region] = {};
                }
                
                if (!allStatistics[region][fileType]) {
                    allStatistics[region][fileType] = {
                        total: 0,
                        details: {}
                    };
                }
                
                allStatistics[region][fileType].total++;
                
                // 根据文件类型统计不同的状态
                if (fileType === '采购申请单') {
                    if (status === '暂存' || status === '已提交') {
                        if (!allStatistics[region][fileType].details[status]) {
                            allStatistics[region][fileType].details[status] = 0;
                        }
                        allStatistics[region][fileType].details[status]++;
                    }
                } else if (fileType === '财务应付单') {
                    if (status && status.includes('未核销')) {
                        if (!allStatistics[region][fileType].details['已审核且未核销']) {
                            allStatistics[region][fileType].details['已审核且未核销'] = 0;
                        }
                        allStatistics[region][fileType].details['已审核且未核销']++;
                    }
                } else if (fileType === '原始订单未下推') {
                    if (status === '已审核' || (status && !status.includes('已下推'))) {
                        if (!allStatistics[region][fileType].details['已审核且未下推']) {
                            allStatistics[region][fileType].details['已审核且未下推'] = 0;
                        }
                        allStatistics[region][fileType].details['已审核且未下推']++;
                    }
                } else {
                    // 其他类型统计暂存状态
                    if (status === '暂存') {
                        if (!allStatistics[region][fileType].details['暂存']) {
                            allStatistics[region][fileType].details['暂存'] = 0;
                        }
                        allStatistics[region][fileType].details['暂存']++;
                    }
                }
            }
        }
    }

    // 显示批量分析结果
    function displayBatchResults() {
        const existingPanel = document.getElementById('batch-results-panel');
        if (existingPanel) {
            existingPanel.remove();
        }
        
        const panel = document.createElement('div');
        panel.id = 'batch-results-panel';
        panel.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10000;
            width: 90%;
            max-width: 1200px;
            max-height: 80vh;
            background: white;
            border: 2px solid #2196F3;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            padding: 25px;
            overflow-y: auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        const title = document.createElement('h2');
        title.textContent = `📊 批量统计分析结果`;
        title.style.cssText = 'margin-top: 0; color: #333; text-align: center; font-size: 24px;';
        
        const summary = document.createElement('div');
        summary.innerHTML = `
            <div style="margin-bottom: 20px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <div style="text-align: center;">
                    <div style="font-size: 14px; color: #666;">处理文件数</div>
                    <div style="font-size: 24px; font-weight: bold; color: #2196F3;">${totalFiles}</div>
                </div>
            </div>
        `;
        
        // 创建结果表格
        const table = document.createElement('table');
        table.style.cssText = `
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        `;
        
        // 创建表头
        const header = document.createElement('thead');
        header.innerHTML = `
            <tr style="background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); color: white;">
                <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">销售区域</th>
                <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">采购订单暂存</th>
                <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">采购申请单<br>(暂存/已提交)</th>
                <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">财务应付单<br>(已审核未核销)</th>
                <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">销售订单暂存</th>
                <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">原始订单<br>(已审核未下推)</th>
            </tr>
        `;
        
        // 创建表体
        const tbody = document.createElement('tbody');
        let rowIndex = 0;
        
        // 按区域名称排序
        const sortedRegions = Object.keys(allStatistics).sort();
        
        sortedRegions.forEach(region => {
            const row = document.createElement('tr');
            row.style.backgroundColor = rowIndex % 2 === 0 ? '#fff' : '#f8f9fa';
            
            const regionData = allStatistics[region];
            
            // 获取各类型数据
            const purchaseOrderTemp = regionData['采购订单暂存']?.details['暂存'] || 0;
            const purchaseApply = regionData['采购申请单'] || { details: {} };
            const purchaseApplyTemp = purchaseApply.details['暂存'] || 0;
            const purchaseApplySubmitted = purchaseApply.details['已提交'] || 0;
            const financePayable = regionData['财务应付单']?.details['已审核且未核销'] || 0;
            const salesOrderTemp = regionData['销售订单暂存']?.details['暂存'] || 0;
            const originalOrderNotPushed = regionData['原始订单未下推']?.details['已审核且未下推'] || 0;
            
            row.innerHTML = `
                <td style="border: 1px solid #ddd; padding: 10px; font-weight: 500;">${region}</td>
                <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">${purchaseOrderTemp}</td>
                <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">${purchaseApplyTemp}/${purchaseApplySubmitted}</td>
                <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">${financePayable}</td>
                <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">${salesOrderTemp}</td>
                <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">${originalOrderNotPushed}</td>
            `;
            
            tbody.appendChild(row);
            rowIndex++;
        });
        
        // 按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = 'margin-top: 20px; text-align: right;';
        
        // 导出Excel按钮
        const exportBtn = document.createElement('button');
        exportBtn.textContent = '💾 导出Excel';
        exportBtn.style.cssText = `
            margin-right: 10px;
            padding: 10px 20px;
            background: linear-gradient(135deg, #28a745 0%, #218838 100%);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        `;
        exportBtn.onmouseenter = function() {
            this.style.transform = 'scale(1.05)';
        };
        exportBtn.onmouseleave = function() {
            this.style.transform = 'scale(1)';
        };
        exportBtn.onclick = () => exportBatchResults();
        
        // 关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.textContent = '✕ 关闭';
        closeBtn.style.cssText = `
            padding: 10px 20px;
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        `;
        closeBtn.onmouseenter = function() {
            this.style.transform = 'scale(1.05)';
        };
        closeBtn.onmouseleave = function() {
            this.style.transform = 'scale(1)';
        };
        closeBtn.onclick = () => panel.remove();
        
        // 组装界面
        table.appendChild(header);
        table.appendChild(tbody);
        buttonContainer.appendChild(exportBtn);
        buttonContainer.appendChild(closeBtn);
        
        panel.appendChild(title);
        panel.appendChild(summary);
        panel.appendChild(table);
        panel.appendChild(buttonContainer);
        
        document.body.appendChild(panel);
    }

    // 导出批量分析结果为Excel
    function exportBatchResults() {
        if (!window.XLSX) {
            alert('Excel库未加载，无法导出');
            return;
        }
        
        // 准备数据
        const wsData = [
            ['销售区域', '采购订单暂存', '采购申请单暂存', '采购申请单已提交', '财务应付单已审核未核销', '销售订单暂存', '原始订单已审核未下推']
        ];
        
        const sortedRegions = Object.keys(allStatistics).sort();
        
        sortedRegions.forEach(region => {
            const regionData = allStatistics[region];
            
            const purchaseOrderTemp = regionData['采购订单暂存']?.details['暂存'] || 0;
            const purchaseApply = regionData['采购申请单'] || { details: {} };
            const purchaseApplyTemp = purchaseApply.details['暂存'] || 0;
            const purchaseApplySubmitted = purchaseApply.details['已提交'] || 0;
            const financePayable = regionData['财务应付单']?.details['已审核且未核销'] || 0;
            const salesOrderTemp = regionData['销售订单暂存']?.details['暂存'] || 0;
            const originalOrderNotPushed = regionData['原始订单未下推']?.details['已审核且未下推'] || 0;
            
            wsData.push([
                region,
                purchaseOrderTemp,
                purchaseApplyTemp,
                purchaseApplySubmitted,
                financePayable,
                salesOrderTemp,
                originalOrderNotPushed
            ]);
        });
        
        // 创建工作簿
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet(wsData);
        
        // 设置列宽
        ws['!cols'] = [
            { wch: 20 }, // 销售区域
            { wch: 15 }, // 采购订单暂存
            { wch: 15 }, // 采购申请单暂存
            { wch: 15 }, // 采购申请单已提交
            { wch: 20 }, // 财务应付单已审核未核销
            { wch: 15 }, // 销售订单暂存
            { wch: 20 }  // 原始订单已审核未下推
        ];
        
        XLSX.utils.book_append_sheet(wb, ws, '区域数据状态');
        
        // 导出文件
        const filename = `区域数据状态_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`;
        XLSX.writeFile(wb, filename);
        
        alert('导出成功！');
    }

    // 显示加载状态
    function showLoading(message) {
        const existingLoading = document.getElementById('batch-loading');
        if (existingLoading) {
            existingLoading.remove();
        }
        
        const loading = document.createElement('div');
        loading.id = 'batch-loading';
        loading.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <div class="spinner" style="
                    width: 20px;
                    height: 20px;
                    border: 3px solid #f3f3f3;
                    border-top: 3px solid #2196F3;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                "></div>
                <span>${message || '正在处理中...'}</span>
            </div>
        `;
        loading.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10001;
            padding: 20px 40px;
            background: rgba(0,0,0,0.8);
            color: white;
            border-radius: 8px;
            font-size: 16px;
        `;
        
        // 添加旋转动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        if (!document.querySelector('style[data-batch-analysis]')) {
            style.setAttribute('data-batch-analysis', 'true');
            document.head.appendChild(style);
        }
        
        document.body.appendChild(loading);
    }

    // 更新加载进度
    function updateLoadingProgress(message) {
        const loading = document.getElementById('batch-loading');
        if (loading) {
            const span = loading.querySelector('span');
            if (span) {
                span.textContent = message;
            }
        }
    }

    // 隐藏加载状态
    function hideLoading() {
        const loading = document.getElementById('batch-loading');
        if (loading) {
            loading.remove();
        }
    }

})();